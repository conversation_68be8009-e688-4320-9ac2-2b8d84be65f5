import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gatepass_flutter/views/onboarding/role_selection.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:gatepass_flutter/utils/app_text.dart';
import 'package:gatepass_flutter/utils/colors.dart';
import 'package:gatepass_flutter/utils/hive_keys.dart';

late Box box;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Open the auth box
  box = await Hive.openBox(HiveKeys.authBox);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return  ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return  MaterialApp(
                title: AppText.appName,
                debugShowCheckedModeBanner: false,
                theme: ThemeData(
                  colorScheme: ColorScheme.fromSeed(seedColor: kPrimaryColor),
                  useMaterial3: true,
                ),
                home: RoleSelectionView(),
                builder: EasyLoading.init(),
              );
            },
          );
        
      
    
  }
}
