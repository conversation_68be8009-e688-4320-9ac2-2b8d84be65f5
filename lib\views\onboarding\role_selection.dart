import 'package:flutter/material.dart';
import '../../utils/assets.dart';

class RoleSelectionView extends StatefulWidget {
  const RoleSelectionView({super.key});

  @override
  State<RoleSelectionView> createState() => _RoleSelectionViewState();
}

class _RoleSelectionViewState extends State<RoleSelectionView> {
  String? selectedRole;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 40),
              const Text(
                'What\nteam suits you?',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D3748),
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(
                child: Column(
                  children: [
                    _buildRoleCard(
                      role: 'admin',
                      title: 'Iron-pumpers',
                      description:
                          'I want to build muscles & have access\nto all gym equipments as I need it\nquestions.',
                      backgroundColor: const Color(0xFFBFD7FF),
                      borderColor: const Color(0xFF4A90E2),
                      image: Assets.admin,
                    ),
                    const SizedBox(height: 16),
                    _buildRoleCard(
                      role: 'promoter',
                      title: 'Going Light',
                      description:
                          'I want to lose weight and just do light\nexercise.',
                      backgroundColor: const Color(0xFFFFF2CC),
                      borderColor: const Color(0xFFFFB84D),
                      image: Assets.promoter,
                    ),
                    const SizedBox(height: 16),
                    _buildRoleCard(
                      role: 'customer',
                      title: 'Celestial Beings',
                      description:
                          'I want to live life and meditate like\nbuddha and yoga with me.',
                      backgroundColor: const Color(0xFFD4F4DD),
                      borderColor: const Color(0xFF68D391),
                      image: Assets.user,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCard({
    required String role,
    required String title,
    required String description,
    required Color backgroundColor,
    required Color borderColor,
    required String image,
  }) {
    final isSelected = selectedRole == role;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedRole = role;
        });
      },
      child: SizedBox(
        width: double.infinity,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // Card container
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              width: double.infinity,
              height: 100,
              margin: const EdgeInsets.only(top: 60),
              child: Card(
                elevation: isSelected ? 12 : 4,
                shadowColor: _getShadowColor(role).withValues(alpha: 0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                  side: BorderSide(
                    color: isSelected
                        ? borderColor
                        : borderColor.withValues(alpha: 0.3),
                    width: isSelected ? 3 : 2,
                  ),
                ),
                color: backgroundColor,
                child: Container(
                  padding: const EdgeInsets.only(
                    top: 40,
                    bottom: 20,
                    left: 20,
                    right: 20,
                  ),
                  child: Center(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: _getTitleColor(role),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ),
            // Image positioned above the card
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Center(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  width: isSelected ? 140 : 120,
                  height: isSelected ? 140 : 120,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: _getShadowColor(
                          role,
                        ).withValues(alpha: isSelected ? 0.2 : 0.1),
                        blurRadius: isSelected ? 16 : 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: borderColor.withValues(alpha: 0.2),
                        width: 2,
                      ),
                    ),
                    padding: const EdgeInsets.all(16),
                    child: Image.asset(image, fit: BoxFit.contain),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getTitleColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF2B6CB0);
      case 'promoter':
        return const Color(0xFFD69E2E);
      case 'customer':
        return const Color(0xFF38A169);
      default:
        return const Color(0xFF2D3748);
    }
  }

  Color _getShadowColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF2563EB); // Blue shadow
      case 'promoter':
        return const Color(0xFFF59E0B); // Orange shadow
      case 'customer':
        return const Color(0xFF10B981); // Green shadow
      default:
        return const Color(0xFF6B7280); // Gray shadow
    }
  }
}
