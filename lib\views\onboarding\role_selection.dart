import 'package:flutter/material.dart';
import '../../utils/assets.dart';

class RoleSelectionView extends StatefulWidget {
  const RoleSelectionView({super.key});

  @override
  State<RoleSelectionView> createState() => _RoleSelectionViewState();
}

class _RoleSelectionViewState extends State<RoleSelectionView> {
  String? selectedRole;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              const Text(
                'What\nteam suits you?',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1A202C),
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(
                child: <PERSON><PERSON>ie<PERSON>(
                  children: [
                    _buildRoleCard(
                      role: 'admin',
                      title: 'Admin',
                      backgroundColor: const Color(0xFFBFD7FF),
                      borderColor: const Color(0xFF4A90E2),
                      image: Assets.admin,
                    ),
                    const SizedBox(height: 30),
                    _buildRoleCard(
                      role: 'promoter',
                      title: 'Promoter',
                      backgroundColor: const Color(0xFFFFF2CC),
                      borderColor: const Color(0xFFFFB84D),
                      image: Assets.promoter,
                    ),
                    const SizedBox(height: 30),
                    _buildRoleCard(
                      role: 'customer',
                      title: 'Customer',
                      backgroundColor: const Color(0xFFD4F4DD),
                      borderColor: const Color(0xFF68D391),
                      image: Assets.user,
                    ),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCard({
    required String role,
    required String title,
    required Color backgroundColor,
    required Color borderColor,
    required String image,
  }) {
    final isSelected = selectedRole == role;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedRole = role;
        });
      },
      child: Container(
        height: 180,
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            // Main Card
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                height: 120,
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: isSelected
                        ? borderColor
                        : borderColor.withValues(alpha: 0.4),
                    width: isSelected ? 3 : 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: _getShadowColor(
                        role,
                      ).withValues(alpha: isSelected ? 0.25 : 0.12),
                      blurRadius: isSelected ? 20 : 12,
                      offset: Offset(0, isSelected ? 8 : 4),
                      spreadRadius: isSelected ? 2 : 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Container(
                  padding: const EdgeInsets.only(
                    top: 50,
                    bottom: 20,
                    left: 20,
                    right: 20,
                  ),
                  child: Center(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 26,
                        fontWeight: FontWeight.bold,
                        color: _getTitleColor(role),
                        letterSpacing: 0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ),
            // Floating Image
            Positioned(
              top: 0,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                width: isSelected ? 110 : 100,
                height: isSelected ? 110 : 100,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: borderColor.withValues(alpha: 0.3),
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: _getShadowColor(
                        role,
                      ).withValues(alpha: isSelected ? 0.3 : 0.15),
                      blurRadius: isSelected ? 24 : 16,
                      offset: Offset(0, isSelected ? 8 : 4),
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Image.asset(image, fit: BoxFit.contain),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getTitleColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF2B6CB0);
      case 'promoter':
        return const Color(0xFFD69E2E);
      case 'customer':
        return const Color(0xFF38A169);
      default:
        return const Color(0xFF2D3748);
    }
  }

  Color _getShadowColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF2563EB); // Blue shadow
      case 'promoter':
        return const Color(0xFFF59E0B); // Orange shadow
      case 'customer':
        return const Color(0xFF10B981); // Green shadow
      default:
        return const Color(0xFF6B7280); // Gray shadow
    }
  }
}
