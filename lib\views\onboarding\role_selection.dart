import 'package:flutter/material.dart';
import 'package:gatepass_flutter/components/common_image_view.dart';
import '../../utils/assets.dart';

class RoleSelectionView extends StatefulWidget {
  const RoleSelectionView({super.key});

  @override
  State<RoleSelectionView> createState() => _RoleSelectionViewState();
}

class _RoleSelectionViewState extends State<RoleSelectionView> {
  String? selectedRole;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              const Text(
                'What\nteam suits you?',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1A202C),
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(
                child: ListView(
                  children: [
                    _buildRoleCard(
                      role: 'admin',
                      title: 'Iron-pumpers',
                      description:
                          'I want to build muscles & have access\nto all gym equipments as I need it\nquestions.',
                      backgroundColor: const Color(0xFFBFD7FF),
                      borderColor: const Color(0xFF4A90E2),
                      image: Assets.admin,
                    ),
                    const SizedBox(height: 20),
                    _buildRoleCard(
                      role: 'promoter',
                      title: 'Going Light',
                      description:
                          'I want to lose weight and just do light\nexercise.',
                      backgroundColor: const Color(0xFFFFF2CC),
                      borderColor: const Color(0xFFFFB84D),
                      image: Assets.promoter,
                    ),
                    const SizedBox(height: 20),
                    _buildRoleCard(
                      role: 'customer',
                      title: 'Celestial Beings',
                      description:
                          'I want to live life and meditate like\nbuddha and yoga with me.',
                      backgroundColor: const Color(0xFFD4F4DD),
                      borderColor: const Color(0xFF68D391),
                      image: Assets.user,
                    ),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCard({
    required String role,
    required String title,
    required String description,
    required Color backgroundColor,
    required Color borderColor,
    required String image,
  }) {
    final isSelected = selectedRole == role;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedRole = role;
        });
      },
      child: Stack(
        children: [
          SizedBox(height: 200),
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            width: double.infinity,
            height: 140,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected
                    ? borderColor
                    : borderColor.withValues(alpha: 0.5),
                width: isSelected ? 4 : 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: _getShadowColor(
                    role,
                  ).withValues(alpha: isSelected ? 0.25 : 0.12),
                  blurRadius: isSelected ? 16 : 8,
                  offset: Offset(0, isSelected ? 6 : 3),
                  spreadRadius: isSelected ? 1 : 0,
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _getTitleColor(role),
              ),
            ),
          ),
          CommonImageView(imagePath: image, height: 200),
        ],
      ),
    );
  }

  Color _getTitleColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF2B6CB0);
      case 'promoter':
        return const Color(0xFFD69E2E);
      case 'customer':
        return const Color(0xFF38A169);
      default:
        return const Color(0xFF2D3748);
    }
  }

  Color _getDescriptionColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF4A90E2);
      case 'promoter':
        return const Color(0xFFED8936);
      case 'customer':
        return const Color(0xFF48BB78);
      default:
        return const Color(0xFF718096);
    }
  }

  Color _getShadowColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF2563EB); // Blue shadow
      case 'promoter':
        return const Color(0xFFF59E0B); // Orange shadow
      case 'customer':
        return const Color(0xFF10B981); // Green shadow
      default:
        return const Color(0xFF6B7280); // Gray shadow
    }
  }
}
