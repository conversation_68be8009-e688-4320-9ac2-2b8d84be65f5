import 'package:flutter/material.dart';
import '../../utils/assets.dart';

class RoleSelectionView extends StatefulWidget {
  const RoleSelectionView({super.key});

  @override
  State<RoleSelectionView> createState() => _RoleSelectionViewState();
}

class _RoleSelectionViewState extends State<RoleSelectionView> {
  String? selectedRole;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 40),
              const Text(
                'What\nteam suits you?',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D3748),
                  height: 1.2,
                ),
              ),
              const SizedBox(height: 40),
              Expanded(
                child: Column(
                  children: [
                    _buildRoleCard(
                      role: 'admin',
                      title: 'Iron-pumpers',
                      description:
                          'I want to build muscles & have access\nto all gym equipments as I need it\nquestions.',
                      backgroundColor: const Color(0xFFBFD7FF),
                      borderColor: const Color(0xFF4A90E2),
                      image: Assets.admin,
                    ),
                    const SizedBox(height: 16),
                    _buildRoleCard(
                      role: 'promoter',
                      title: 'Going Light',
                      description:
                          'I want to lose weight and just do light\nexercise.',
                      backgroundColor: const Color(0xFFFFF2CC),
                      borderColor: const Color(0xFFFFB84D),
                      image: Assets.promoter,
                    ),
                    const SizedBox(height: 16),
                    _buildRoleCard(
                      role: 'customer',
                      title: 'Celestial Beings',
                      description:
                          'I want to live life and meditate like\nbuddha and yoga with me.',
                      backgroundColor: const Color(0xFFD4F4DD),
                      borderColor: const Color(0xFF68D391),
                      image: Assets.user,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCard({
    required String role,
    required String title,
    required String description,
    required Color backgroundColor,
    required Color borderColor,
    required String image,
  }) {
    final isSelected = selectedRole == role;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedRole = role;
        });
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? borderColor
                : borderColor.withValues(alpha: 0.3),
            width: isSelected ? 3 : 2,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: borderColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Image.asset(image, fit: BoxFit.contain),
            ),
            const SizedBox(width: 24),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: _getTitleColor(role),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 15,
                      color: _getDescriptionColor(role),
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getTitleColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF2B6CB0);
      case 'promoter':
        return const Color(0xFFD69E2E);
      case 'customer':
        return const Color(0xFF38A169);
      default:
        return const Color(0xFF2D3748);
    }
  }

  Color _getDescriptionColor(String role) {
    switch (role) {
      case 'admin':
        return const Color(0xFF4A90E2);
      case 'promoter':
        return const Color(0xFFED8936);
      case 'customer':
        return const Color(0xFF48BB78);
      default:
        return const Color(0xFF718096);
    }
  }
}
