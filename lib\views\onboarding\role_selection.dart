import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gatepass_flutter/models/user_role.dart';

class RoleSelectionView extends StatefulWidget {
  const RoleSelectionView({super.key});

  @override
  State<RoleSelectionView> createState() => _RoleSelectionViewState();
}

class _RoleSelectionViewState extends State<RoleSelectionView> {
  UserRole? selectedRole;

  void _onStartPressed() {
    if (selectedRole == null) return;
    switch (selectedRole) {
      case UserRole.customer:
        context.go('/login/customer');
        break;
      case UserRole.promoter:
        context.go('/login/promoter');
        break;
      case UserRole.admin:
        context.go('/login/admin');
        break;
      default:
        break;
    }
  }

  Widget _roleCard({
    required UserRole role,
    required String title,
    required String subtitle,
    required Widget characterWidget,
    bool showStars = false,
  }) {
    final isSelected = selectedRole == role;
    return GestureDetector(
      onTap: () => setState(() => selectedRole = role),
      child: Container(
        width: double.infinity,
        height: 140,
        margin: const EdgeInsets.symmetric(vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0xFF6B46C1),
          borderRadius: BorderRadius.circular(32),
          border: Border.all(
            color: isSelected ? const Color(0xFF4C1D95) : Colors.transparent,
            width: 3,
          ),
        ),
        child: Stack(
          children: [
            // Character illustration positioned on the left
            Positioned(
              left: 16,
              top: 16,
              bottom: 16,
              child: SizedBox(width: 120, child: characterWidget),
            ),
            // Text content positioned on the right
            Positioned(
              right: 24,
              top: 0,
              bottom: 0,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            // Small decorative stars
            if (showStars) ...[
              const Positioned(
                right: 16,
                top: 16,
                child: Icon(Icons.star, color: Colors.white, size: 14),
              ),
              const Positioned(
                right: 40,
                bottom: 20,
                child: Icon(Icons.star, color: Colors.white, size: 10),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCharacterIllustration({
    required String skinColor,
    required String hairColor,
    required String clothingColor,
    required bool isAdmin,
  }) {
    return Container(
      child: Stack(
        children: [
          // Simple character representation
          Positioned(
            left: 20,
            bottom: 10,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Head
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color(int.parse(skinColor)),
                    shape: BoxShape.circle,
                  ),
                  child: Stack(
                    children: [
                      // Hair
                      Positioned(
                        top: 0,
                        left: 5,
                        right: 5,
                        child: Container(
                          height: 20,
                          decoration: BoxDecoration(
                            color: Color(int.parse(hairColor)),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20),
                            ),
                          ),
                        ),
                      ),
                      // Eyes
                      const Positioned(
                        top: 15,
                        left: 10,
                        child: Icon(Icons.circle, size: 4, color: Colors.black),
                      ),
                      const Positioned(
                        top: 15,
                        right: 10,
                        child: Icon(Icons.circle, size: 4, color: Colors.black),
                      ),
                      // Smile
                      Positioned(
                        bottom: 8,
                        left: 15,
                        right: 15,
                        child: Container(
                          height: 2,
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 4),
                // Body
                Container(
                  width: 30,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color(int.parse(clothingColor)),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: isAdmin
                      ? const Center(
                          child: Icon(
                            Icons.business_center,
                            size: 16,
                            color: Colors.white,
                          ),
                        )
                      : const Center(
                          child: Icon(
                            Icons.person,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F3F0),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
          child: Column(
            children: [
              // Decorative stars at top
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Transform.rotate(
                    angle: 0.3,
                    child: const Icon(
                      Icons.star_outline,
                      color: Color(0xFF7B68EE),
                      size: 24,
                    ),
                  ),
                  Transform.rotate(
                    angle: -0.3,
                    child: const Icon(
                      Icons.star_outline,
                      color: Color(0xFFFF6B9D),
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 40),

              // Title
              const Text(
                'Choose your role\nbelow',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w800,
                  color: Color(0xFF2D2D2D),
                  height: 1.3,
                ),
              ),
              const SizedBox(height: 20),

              // Curved arrow
              Transform.rotate(
                angle: 0.2,
                child: const Icon(
                  Icons.south_east_rounded,
                  size: 28,
                  color: Color(0xFF2D2D2D),
                ),
              ),
              const SizedBox(height: 40),

              // Admin role card - matching "Deaf or Hard of Hearing"
              _roleCard(
                role: UserRole.admin,
                title: 'Admin',
                subtitle: 'System Management',
                characterWidget: _buildCharacterIllustration(
                  skinColor: '0xFFFFDBB5',
                  hairColor: '0xFF2D2D2D',
                  clothingColor: '0xFF2D2D2D',
                  isAdmin: true,
                ),
                showStars: true,
              ),

              const SizedBox(height: 20),

              // "or" text
              const Text(
                'or',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF6B7280),
                ),
              ),

              const SizedBox(height: 20),

              // Customer role card - matching "Sign Language Interpreter"
              _roleCard(
                role: UserRole.customer,
                title: 'Customer',
                subtitle: 'Service Access',
                characterWidget: _buildCharacterIllustration(
                  skinColor: '0xFFFFDBB5',
                  hairColor: '0xFF8B4513',
                  clothingColor: '0xFF2D2D2D',
                  isAdmin: false,
                ),
              ),

              const Spacer(),

              // Decorative stars at bottom
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Transform.rotate(
                    angle: -0.2,
                    child: const Icon(
                      Icons.star_outline,
                      color: Color(0xFFFFB347),
                      size: 18,
                    ),
                  ),
                  Transform.rotate(
                    angle: 0.4,
                    child: const Icon(
                      Icons.star_outline,
                      color: Color(0xFF7B68EE),
                      size: 22,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Get started button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: selectedRole != null ? _onStartPressed : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2D2D2D),
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: Colors.grey.shade300,
                    disabledForegroundColor: Colors.grey.shade600,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        "Get started",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward, size: 18),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
